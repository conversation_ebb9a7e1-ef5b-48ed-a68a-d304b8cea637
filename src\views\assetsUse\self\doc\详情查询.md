

## 借出信息-详情查询


**接口地址**:`/jeecg-boot/biz/selfuse/detail`


**请求方式**:`GET`


**请求数据类型**:`application/x-www-form-urlencoded`


**响应数据类型**:`*/*`


**接口描述**:


**请求参数**:


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|id|id|query|true|string||
|X-Access-Token|token|header|false|string||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|接口返回对象«资产自用信息»|
|401|Unauthorized||
|403|Forbidden||
|404|Not Found||


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|返回代码|integer(int32)|integer(int32)|
|message|返回处理消息|string||
|result|返回数据对象|资产自用信息|资产自用信息|
|&emsp;&emsp;assetCode|资产编号|string||
|&emsp;&emsp;assetId|资产类型Id|integer(int32)||
|&emsp;&emsp;assetName|资产项目(资产名称)|string||
|&emsp;&emsp;assetType|资产类型|integer(int32)||
|&emsp;&emsp;bookValueDate|账面价值时点|string(date-time)||
|&emsp;&emsp;createBy|创建人|string||
|&emsp;&emsp;createTime|创建时间|string(date-time)||
|&emsp;&emsp;handlerUserName|经办人|string||
|&emsp;&emsp;hasIncome|是否有收益|integer(int32)||
|&emsp;&emsp;hasOfficeStandard|是否制定办公用房标准|integer(int32)||
|&emsp;&emsp;id|ID|integer(int32)||
|&emsp;&emsp;inputTime|录入时间|string(date-time)||
|&emsp;&emsp;inputUserName|录入人|string||
|&emsp;&emsp;isExceedStandard|是否超标|integer(int32)||
|&emsp;&emsp;manageCompany|管理单位Id|integer(int32)||
|&emsp;&emsp;manageCompanyName|管理单位|string||
|&emsp;&emsp;occupiedAssetName|被占用资产名称|string||
|&emsp;&emsp;remark|备注|string||
|&emsp;&emsp;reported|是否报送国资委|integer(int32)||
|&emsp;&emsp;selfUseArea|自用面积|number||
|&emsp;&emsp;selfUseBookValue|自用资产账面价值|number||
|&emsp;&emsp;selfUseDays|自用天数|integer(int32)||
|&emsp;&emsp;selfUseEndDate|自用结束日期|string(date-time)||
|&emsp;&emsp;selfUseOriginalValue|自用资产原值|number||
|&emsp;&emsp;selfUseStartDate|自用起始日期|string(date-time)||
|&emsp;&emsp;status|状态|integer(int32)||
|&emsp;&emsp;supervisionCode|序号:数据传到国资监管平台后将返回序号|string||
|&emsp;&emsp;updateBy|更新人|string||
|&emsp;&emsp;updateTime|更新时间|string(date-time)||
|&emsp;&emsp;usePurpose|使用用途|string||
|success|成功标志|boolean||
|timestamp|时间戳|integer(int64)|integer(int64)|


**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"result": {
		"assetCode": "",
		"assetId": 0,
		"assetName": "",
		"assetType": 0,
		"bookValueDate": "",
		"createBy": "",
		"createTime": "",
		"handlerUserName": "",
		"hasIncome": 0,
		"hasOfficeStandard": 0,
		"id": 0,
		"inputTime": "",
		"inputUserName": "",
		"isExceedStandard": 0,
		"manageCompany": 0,
		"manageCompanyName": "",
		"occupiedAssetName": "",
		"remark": "",
		"reported": 0,
		"selfUseArea": 0,
		"selfUseBookValue": 0,
		"selfUseDays": 0,
		"selfUseEndDate": "",
		"selfUseOriginalValue": 0,
		"selfUseStartDate": "",
		"status": 0,
		"supervisionCode": "",
		"updateBy": "",
		"updateTime": "",
		"usePurpose": ""
	},
	"success": true,
	"timestamp": 0
}
```