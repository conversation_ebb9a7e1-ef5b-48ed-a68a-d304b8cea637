

## 自用信息-分页列表查询


**接口地址**:`/jeecg-boot/biz/selfuse/page`


**请求方式**:`POST`


**请求数据类型**:`application/json`


**响应数据类型**:`*/*`


**接口描述**:


**请求示例**:


```javascript
{
  "assetCode": "",
  "assetProjectName": "",
  "assetType": "",
  "handlerUserName": "",
  "hasIncome": 0,
  "inputUserName": "",
  "isExceedStandard": 0,
  "manageCompanyName": "",
  "maxInputTime": "",
  "maxSelfUseArea": 0,
  "maxSelfUseBookValue": 0,
  "maxSelfUseDays": 0,
  "maxSelfUseEndDate": "",
  "maxSelfUseStartDate": "",
  "maxUpdateTime": "",
  "minInputTime": "",
  "minSelfUseArea": 0,
  "minSelfUseBookValue": 0,
  "minSelfUseDays": 0,
  "minSelfUseEndDate": "",
  "minSelfUseStartDate": "",
  "minUpdateTime": "",
  "occupiedAssetName": "",
  "pageNo": 0,
  "pageSize": 0,
  "reported": 0,
  "usePurpose": ""
}
```


**请求参数**:


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|queryRo|queryRo|body|true|自用信息检索|自用信息检索|
|&emsp;&emsp;assetCode|资产编号||false|string||
|&emsp;&emsp;assetProjectName|资产项目名称||false|string||
|&emsp;&emsp;assetType|资产类型||false|string||
|&emsp;&emsp;handlerUserName|经办人||false|string||
|&emsp;&emsp;hasIncome|是否有收益||false|integer(int32)||
|&emsp;&emsp;inputUserName|录入人||false|string||
|&emsp;&emsp;isExceedStandard|是否超标||false|integer(int32)||
|&emsp;&emsp;manageCompanyName|管理单位||false|string||
|&emsp;&emsp;maxInputTime|录入时间-最大||false|string(date-time)||
|&emsp;&emsp;maxSelfUseArea|自用面积-最大||false|number||
|&emsp;&emsp;maxSelfUseBookValue|自用资产账面价值-最大||false|number||
|&emsp;&emsp;maxSelfUseDays|自用天数-最大||false|integer(int32)||
|&emsp;&emsp;maxSelfUseEndDate|自用结束日期-最大||false|string(date-time)||
|&emsp;&emsp;maxSelfUseStartDate|自用起始日期-最大||false|string(date-time)||
|&emsp;&emsp;maxUpdateTime|更新时间-最大||false|string(date-time)||
|&emsp;&emsp;minInputTime|录入时间-最小||false|string(date-time)||
|&emsp;&emsp;minSelfUseArea|自用面积-最小||false|number||
|&emsp;&emsp;minSelfUseBookValue|自用资产账面价值-最小||false|number||
|&emsp;&emsp;minSelfUseDays|自用天数-最小||false|integer(int32)||
|&emsp;&emsp;minSelfUseEndDate|自用结束日期-最小||false|string(date-time)||
|&emsp;&emsp;minSelfUseStartDate|自用起始日期-最小||false|string(date-time)||
|&emsp;&emsp;minUpdateTime|更新时间-最小||false|string(date-time)||
|&emsp;&emsp;occupiedAssetName|被占用资产名称||false|string||
|&emsp;&emsp;pageNo|||false|integer(int32)||
|&emsp;&emsp;pageSize|||false|integer(int32)||
|&emsp;&emsp;reported|是否报送国资委||false|integer(int32)||
|&emsp;&emsp;usePurpose|使用用途||false|string||
|X-Access-Token|token|header|false|string||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|接口返回对象«IPage«资产自用信息»»|
|201|Created||
|401|Unauthorized||
|403|Forbidden||
|404|Not Found||


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|返回代码|integer(int32)|integer(int32)|
|message|返回处理消息|string||
|result|返回数据对象|IPage«资产自用信息»|IPage«资产自用信息»|
|&emsp;&emsp;current||integer(int64)||
|&emsp;&emsp;pages||integer(int64)||
|&emsp;&emsp;records||array|资产自用信息|
|&emsp;&emsp;&emsp;&emsp;assetCode|资产编号|string||
|&emsp;&emsp;&emsp;&emsp;assetId|资产类型Id|integer||
|&emsp;&emsp;&emsp;&emsp;assetName|资产项目(资产名称)|string||
|&emsp;&emsp;&emsp;&emsp;assetType|资产类型|integer||
|&emsp;&emsp;&emsp;&emsp;bookValueDate|账面价值时点|string||
|&emsp;&emsp;&emsp;&emsp;createBy|创建人|string||
|&emsp;&emsp;&emsp;&emsp;createTime|创建时间|string||
|&emsp;&emsp;&emsp;&emsp;handlerUserName|经办人|string||
|&emsp;&emsp;&emsp;&emsp;hasIncome|是否有收益|integer||
|&emsp;&emsp;&emsp;&emsp;hasOfficeStandard|是否制定办公用房标准|integer||
|&emsp;&emsp;&emsp;&emsp;id|ID|integer||
|&emsp;&emsp;&emsp;&emsp;inputTime|录入时间|string||
|&emsp;&emsp;&emsp;&emsp;inputUserName|录入人|string||
|&emsp;&emsp;&emsp;&emsp;isExceedStandard|是否超标|integer||
|&emsp;&emsp;&emsp;&emsp;manageCompany|管理单位Id|integer||
|&emsp;&emsp;&emsp;&emsp;manageCompanyName|管理单位|string||
|&emsp;&emsp;&emsp;&emsp;occupiedAssetName|被占用资产名称|string||
|&emsp;&emsp;&emsp;&emsp;remark|备注|string||
|&emsp;&emsp;&emsp;&emsp;reported|是否报送国资委|integer||
|&emsp;&emsp;&emsp;&emsp;selfUseArea|自用面积|number||
|&emsp;&emsp;&emsp;&emsp;selfUseBookValue|自用资产账面价值|number||
|&emsp;&emsp;&emsp;&emsp;selfUseDays|自用天数|integer||
|&emsp;&emsp;&emsp;&emsp;selfUseEndDate|自用结束日期|string||
|&emsp;&emsp;&emsp;&emsp;selfUseOriginalValue|自用资产原值|number||
|&emsp;&emsp;&emsp;&emsp;selfUseStartDate|自用起始日期|string||
|&emsp;&emsp;&emsp;&emsp;status|状态|integer||
|&emsp;&emsp;&emsp;&emsp;supervisionCode|序号:数据传到国资监管平台后将返回序号|string||
|&emsp;&emsp;&emsp;&emsp;updateBy|更新人|string||
|&emsp;&emsp;&emsp;&emsp;updateTime|更新时间|string||
|&emsp;&emsp;&emsp;&emsp;usePurpose|使用用途|string||
|&emsp;&emsp;size||integer(int64)||
|&emsp;&emsp;total||integer(int64)||
|success|成功标志|boolean||
|timestamp|时间戳|integer(int64)|integer(int64)|


**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"result": {
		"current": 0,
		"pages": 0,
		"records": [
			{
				"assetCode": "",
				"assetId": 0,
				"assetName": "",
				"assetType": 0,
				"bookValueDate": "",
				"createBy": "",
				"createTime": "",
				"handlerUserName": "",
				"hasIncome": 0,
				"hasOfficeStandard": 0,
				"id": 0,
				"inputTime": "",
				"inputUserName": "",
				"isExceedStandard": 0,
				"manageCompany": 0,
				"manageCompanyName": "",
				"occupiedAssetName": "",
				"remark": "",
				"reported": 0,
				"selfUseArea": 0,
				"selfUseBookValue": 0,
				"selfUseDays": 0,
				"selfUseEndDate": "",
				"selfUseOriginalValue": 0,
				"selfUseStartDate": "",
				"status": 0,
				"supervisionCode": "",
				"updateBy": "",
				"updateTime": "",
				"usePurpose": ""
			}
		],
		"size": 0,
		"total": 0
	},
	"success": true,
	"timestamp": 0
}
```