<template>
  <div class="self-form">
    <div class="simple-title">{{ isUpdate ? '编辑自用信息' : '新增自用信息' }}</div>
    <div class="p-4">
      <a-form :model="formData" :rules="rules" ref="selfFormRef" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <!-- 基本信息 -->
        <div class="form-card">
          <div class="form-card-header">
            <div class="form-card-title">
              <Icon icon="ant-design:info-circle-outlined" class="title-icon" />
              基本信息
            </div>
          </div>
          <div class="form-card-body">
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item name="id">
                  <template #label>
                    <span>序号</span>
                    <a-tooltip title="数据传到国资监管平台后将返回序号">
                      <Icon icon="ant-design:question-circle-outlined" class="tooltip-icon" />
                    </a-tooltip>
                  </template>
                  <a-input v-model:value="formData.id" placeholder="序号为只读项" disabled />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="type" label="资产类型">
                  <a-select v-model:value="formData.type" placeholder="请选择资产类型" @change="handleAssetTypeChange">
                    <a-select-option :value="0">土地</a-select-option>
                    <a-select-option :value="1">房屋</a-select-option>
                    <a-select-option :value="2">设备</a-select-option>
                    <a-select-option :value="3">广告位</a-select-option>
                    <a-select-option :value="4">其他</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="name" label="资产项目（资产名称）">
                  <a-select
                    v-model:value="formData.name"
                    placeholder="请选择资产项目（资产名称）"
                    show-search
                    :filter-option="filterOption"
                    @change="handleAssetProjectChange"
                  >
                    <a-select-option value="土地资产1">土地资产1</a-select-option>
                    <a-select-option value="房屋资产1">房屋资产1</a-select-option>
                    <a-select-option value="设备资产1">设备资产1</a-select-option>
                    <a-select-option value="广告位资产1">广告位资产1</a-select-option>
                    <a-select-option value="其他资产1">其他资产1</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="code" label="资产编号">
                  <a-input v-model:value="formData.code" placeholder="选择资产项目（资产名称）后自动带出" disabled />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="occupyName" label="被占用资产名称">
                  <a-input v-model:value="formData.occupyName" placeholder="请输入被占用资产名称" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="manageUnit" label="管理单位">
                  <a-input v-model:value="formData.manageUnit" placeholder="选择资产项目（资产名称）后自动带出" disabled />
                  <div class="help-text">将使用管理单位作为数据权限判断依据</div>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="reportOrNot" label="是否报送国资委">
                  <a-select v-model:value="formData.reportOrNot" placeholder="请选择">
                    <a-select-option :value="0">否</a-select-option>
                    <a-select-option :value="1">是</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="operator" label="经办人">
                  <a-input v-model:value="formData.operator" placeholder="请输入经办人" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="entryClerk" label="录入人">
                  <a-input v-model:value="formData.entryClerk" placeholder="录入人" disabled />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="createTime" label="录入时间">
                  <a-input v-model:value="formData.createTime" placeholder="录入时间" disabled />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="status">
                  <template #label>
                    <span>状态</span>
                    <a-tooltip title="备案数据支持撤回、草稿数据和撤回数据支持作废">
                      <Icon icon="ant-design:question-circle-outlined" class="tooltip-icon" />
                    </a-tooltip>
                  </template>
                  <a-select v-model:value="formData.status" placeholder="请选择状态">
                    <a-select-option :value="0" :disabled="isDraftDisabled">草稿</a-select-option>
                    <a-select-option :value="1" :disabled="isFiledDisabled">备案</a-select-option>
                    <a-select-option :value="2" :disabled="isRevokedDisabled">撤回</a-select-option>
                    <a-select-option :value="4" :disabled="isVoidDisabled">作废</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </div>

        <!-- 自用信息 -->
        <div class="form-card">
          <div class="form-card-header">
            <div class="form-card-title">
              <Icon icon="ant-design:user-outlined" class="title-icon" />
              自用信息
            </div>
          </div>
          <div class="form-card-body">
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item name="startDate" label="自用起始日期">
                  <a-date-picker
                    v-model:value="formData.startDate"
                    placeholder="请选择自用起始日期"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    :disabled-date="startDateDisabled"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="endDate" label="自用结束日期">
                  <a-date-picker
                    v-model:value="formData.endDate"
                    placeholder="请选择自用结束日期"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    :disabled-date="endDateDisabled"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="selfDays" label="自用天数">
                  <a-input v-model:value="formData.selfDays" placeholder="自动计算" disabled />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="selfArea" label="自用面积（㎡）">
                  <a-input-number
                    v-model:value="formData.selfArea"
                    :precision="2"
                    :min="0"
                    :controls="false"
                    placeholder="请输入自用面积"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="assetsAmount" label="自用资产原值（万元）">
                  <a-input-number
                    v-model:value="formData.assetsAmount"
                    :precision="2"
                    :min="0"
                    :controls="false"
                    placeholder="请输入自用资产原值"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="bookAmount" label="自用资产账面价值（万元）">
                  <a-input-number
                    v-model:value="formData.bookAmount"
                    :precision="2"
                    :min="0"
                    :controls="false"
                    placeholder="请输入自用资产账面价值"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="dateOfBookValue" label="账面价值时点">
                  <a-date-picker
                    v-model:value="formData.dateOfBookValue"
                    placeholder="请选择账面价值时点"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="purpose" label="使用用途">
                  <a-input v-model:value="formData.purpose" placeholder="请输入使用用途" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="profit" label="是否有收益">
                  <a-select v-model:value="formData.profit" placeholder="请选择是否有收益">
                    <a-select-option :value="0">是</a-select-option>
                    <a-select-option :value="1">否</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="officeStandard" label="是否制定办公用房标准">
                  <a-select v-model:value="formData.officeStandard" placeholder="请选择是否制定办公用房标准">
                    <a-select-option :value="0">是</a-select-option>
                    <a-select-option :value="1">否</a-select-option>
                    <a-select-option :value="2">无需制定</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="exceedance" label="是否超标">
                  <a-select v-model:value="formData.exceedance" placeholder="请选择是否超标">
                    <a-select-option :value="0">是</a-select-option>
                    <a-select-option :value="1">否</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="24">
                <a-form-item name="remark" :label-col="{ span: 2 }" label="备注">
                  <a-textarea v-model:value="formData.remark" placeholder="请输入备注" :rows="4" />
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </div>

        <!-- 表单操作按钮 -->
        <div class="form-footer">
          <a-button @click="handleReset">重置</a-button>
          <a-button type="primary" @click="handleSubmit" :loading="submitLoading" style="margin-left: 12px"> 提交 </a-button>
        </div>
      </a-form>
    </div>
  </div>
</template>

<script lang="ts" name="SelfInfoForm" setup>
  import { ref, onMounted, watch, computed, nextTick } from 'vue';
  import { useRouter, useRoute } from 'vue-router';
  import { Icon } from '/@/components/Icon';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { saveOrUpdate, getDetail } from './self.api';
  import dayjs from 'dayjs';
  import { useUserStore } from '/@/store/modules/user';

  // 定义表单数据接口
  interface FormData {
    id: string;
    type: number | null;
    name: string;
    code: string;
    occupyName: string;
    manageUnit: string;
    reportOrNot: number | null;
    operator: string;
    entryClerk: string;
    createTime: string;
    status: number | null;
    startDate: string;
    endDate: string;
    selfDays: string;
    selfArea: number | null;
    assetsAmount: number | null;
    bookAmount: number | null;
    dateOfBookValue: string;
    purpose: string;
    profit: number | null;
    officeStandard: number | null;
    exceedance: number | null;
    remark: string;
  }

  const router = useRouter();
  const route = useRoute();
  const userStore = useUserStore();
  const { createMessage, createConfirm } = useMessage();

  const submitLoading = ref(false);
  const isUpdate = ref(false);
  const recordId = ref('');
  const originalStatus = ref<number | null>(null);
  const selfFormRef = ref();

  // 表单数据
  const formData = ref<FormData>({
    id: '',
    type: null,
    name: '',
    code: '',
    occupyName: '',
    manageUnit: '',
    reportOrNot: 0,
    operator: '',
    entryClerk: userStore.getUserInfo.realname || '当前用户',
    createTime: dayjs().format('YYYY-MM-DD'),
    status: 0,
    startDate: '',
    endDate: '',
    selfDays: '',
    selfArea: null,
    assetsAmount: null,
    bookAmount: null,
    dateOfBookValue: '',
    purpose: '',
    profit: 1,
    officeStandard: 1,
    exceedance: 1,
    remark: '',
  });

  // 表单验证规则
  const rules = {
    // 基本信息验证规则
    type: [{ required: true, message: '请选择资产类型', trigger: 'change' }],
    name: [{ required: true, message: '请选择资产项目（资产名称）', trigger: 'change' }],
    code: [{ required: true, message: '请选择资产项目（资产名称）后自动带出资产编号', trigger: 'change' }],
    manageUnit: [{ required: true, message: '管理单位为必填项', trigger: 'change' }],
    reportOrNot: [{ required: true, message: '请选择是否报送国资委', trigger: 'change' }],
    operator: [{ required: true, message: '请输入经办人', trigger: 'blur' }],
    entryClerk: [{ required: true, message: '录入人为必填项' }],
    createTime: [{ required: true, message: '录入时间为必填项' }],
    status: [{ required: true, message: '请选择状态', trigger: 'change' }],

    // 自用信息验证规则
    startDate: [{ required: true, message: '请选择自用起始日期', trigger: 'change' }],
    endDate: [
      {
        validator: (rule: any, value: string) => {
          if (!value) return Promise.resolve();
          if (!formData.value.startDate) {
            return Promise.reject(new Error('请先选择自用起始日期'));
          }
          const startDate = new Date(formData.value.startDate);
          const endDate = new Date(value);
          if (endDate <= startDate) {
            return Promise.reject(new Error('自用结束日期必须大于自用起始日期'));
          }
          return Promise.resolve();
        },
        trigger: 'change',
      },
    ],
    selfArea: [
      {
        validator: (rule: any, value: number) => {
          if ((formData.value.type === 0 || formData.value.type === 1) && (value === null || value === undefined || value === 0)) {
            return Promise.reject(new Error('请输入自用面积'));
          }
          return Promise.resolve();
        },
        trigger: 'blur',
      },
    ],
    bookAmount: [{ required: true, message: '请输入自用资产账面价值', trigger: 'blur' }],
    dateOfBookValue: [{ required: true, message: '请选择账面价值时点', trigger: 'change' }],
    purpose: [{ required: true, message: '请输入使用用途', trigger: 'blur' }],
  };

  // 计算属性：状态控制
  const isDraftDisabled = computed(() => isUpdate.value);
  const isFiledDisabled = computed(() => {
    if (!isUpdate.value) return false;
    return originalStatus.value !== 0;
  });
  const isRevokedDisabled = computed(() => {
    if (!isUpdate.value) return true;
    return originalStatus.value !== 1;
  });
  const isVoidDisabled = computed(() => {
    if (!isUpdate.value) return true;
    return originalStatus.value === null || ![0, 2].includes(originalStatus.value);
  });

  // 监听资产类型变化，更新自用面积必填状态
  watch(
    () => formData.value.type,
    () => {
      // 触发自用面积字段的重新验证
      nextTick(() => {
        selfFormRef.value?.validateFields(['selfArea']);
      });
    }
  );

  // 监听日期变化，自动计算自用天数
  watch(
    [() => formData.value.startDate, () => formData.value.endDate],
    ([startDate, endDate]) => {
      calculateSelfDays(startDate, endDate);
    }
  );

  // 计算自用天数
  function calculateSelfDays(startDate: string, endDate: string) {
    if (startDate && endDate) {
      const start = new Date(startDate);
      const end = new Date(endDate);
      const diffTime = Math.abs(end.getTime() - start.getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      formData.value.selfDays = diffDays.toString();
    } else if (startDate) {
      const start = new Date(startDate);
      const today = new Date();
      const diffTime = Math.abs(today.getTime() - start.getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      formData.value.selfDays = diffDays.toString();
    } else {
      formData.value.selfDays = '';
    }
  }

  // 验证表单
  async function validate() {
    try {
      await selfFormRef.value?.validate();
      return true;
    } catch (error) {
      return false;
    }
  }

  // 获取表单数据
  function getFormData() {
    return { ...formData.value };
  }

  // 提交表单
  async function handleSubmit() {
    try {
      const isValid = await validate();
      if (!isValid) {
        return;
      }

      submitLoading.value = true;

      const submitData = getFormData();

      // 如果是编辑模式，添加ID
      if (isUpdate.value) {
        submitData.id = recordId.value;
      }

      await saveOrUpdate(submitData, isUpdate.value);
      createMessage.success(isUpdate.value ? '更新成功' : '新增成功');

      // 返回列表页
      router.push('/assets-use/self');
    } catch (error) {
      createMessage.error('操作失败');
    } finally {
      submitLoading.value = false;
    }
  }

  // 重置表单
  function handleReset() {
    createConfirm({
      iconType: 'warning',
      title: '确认重置',
      content: '确定要重置表单吗？所有已填写的数据将会清空',
      onOk: () => {
        resetFields();
        if (!isUpdate.value) {
          setDefaultValues();
        }
        createMessage.success('表单已重置');
      },
    });
  }

  // 重置所有表单
  function resetFields() {
    selfFormRef.value?.resetFields();
    formData.value = {
      id: '',
      type: null,
      name: '',
      code: '',
      occupyName: '',
      manageUnit: '',
      reportOrNot: 0,
      operator: '',
      entryClerk: userStore.getUserInfo.realname || '当前用户',
      createTime: dayjs().format('YYYY-MM-DD'),
      status: 0,
      startDate: '',
      endDate: '',
      selfDays: '',
      selfArea: null,
      assetsAmount: null,
      bookAmount: null,
      dateOfBookValue: '',
      purpose: '',
      profit: 1,
      officeStandard: 1,
      exceedance: 1,
      remark: '',
    };
  }

  // 设置默认值
  function setDefaultValues() {
    formData.value.status = 0; // 默认草稿状态
    formData.value.entryClerk = userStore.getUserInfo.realname || '当前用户'; // 录入人
    formData.value.createTime = dayjs().format('YYYY-MM-DD'); // 录入时间
    formData.value.reportOrNot = 0; // 默认否
    formData.value.profit = 1; // 默认否
    formData.value.officeStandard = 1; // 默认否
    formData.value.exceedance = 1; // 默认否
  }

  // 设置表单值
  function setFieldsValue(data: any) {
    formData.value = {
      id: data.id || '',
      type: data.type,
      name: data.name || '',
      code: data.code || '',
      occupyName: data.occupyName || '',
      manageUnit: data.manageUnit || '',
      reportOrNot: data.reportOrNot,
      operator: data.operator || '',
      entryClerk: data.entryClerk || '',
      createTime: data.createTime || '',
      status: data.status,
      startDate: data.startDate || '',
      endDate: data.endDate || '',
      selfDays: data.selfDays?.toString() || '',
      selfArea: data.selfArea,
      assetsAmount: data.assetsAmount,
      bookAmount: data.bookAmount,
      dateOfBookValue: data.dateOfBookValue || '',
      purpose: data.purpose || '',
      profit: data.profit,
      officeStandard: data.officeStandard,
      exceedance: data.exceedance,
      remark: data.remark || '',
    };
  }

  // 处理资产类型变更
  function handleAssetTypeChange() {
    formData.value.name = '';
    formData.value.code = '';
    formData.value.manageUnit = '';
    formData.value.occupyName = '';
  }

  // 处理资产项目变更
  function handleAssetProjectChange(value: string) {
    // 这里可以根据选择的资产项目自动填充相关信息
    // 模拟自动填充逻辑
    if (value) {
      formData.value.code = `CODE_${value}`;
      formData.value.manageUnit = `管理单位_${value}`;
      formData.value.occupyName = value;
    } else {
      formData.value.code = '';
      formData.value.manageUnit = '';
      formData.value.occupyName = '';
    }
  }

  // 搜索过滤函数
  function filterOption(input: string, option: any) {
    return option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
  }

  // 日期禁用函数
  function startDateDisabled(current: any) {
    // 使用dayjs判断日期是否大于当前日期
    if (current && dayjs(current).isAfter(dayjs(), 'day')) {
      return true;
    }

    // 如果已经选择了结束日期，起始日期不能晚于结束日期
    if (formData.value.endDate) {
      return dayjs(current).isAfter(dayjs(formData.value.endDate), 'day');
    }

    return false;
  }

  function endDateDisabled(current: any) {
    if (!formData.value.startDate) return false;
    const startDate = dayjs(formData.value.startDate).valueOf();
    return startDate >= current.valueOf();
  }

  // 加载详情数据
  async function loadDetail() {
    const id = route.query.id as string;
    if (id) {
      isUpdate.value = true;
      recordId.value = id;
      try {
        const result = await getDetail(id);
        const data = result.data || result;
        setFieldsValue(data);
        originalStatus.value = data.status;
      } catch (error) {
        createMessage.error('加载数据失败');
      }
    } else {
      isUpdate.value = false;
      setDefaultValues();
    }
  }

  onMounted(() => {
    loadDetail();
  });
</script>

<style lang="less" scoped>
  .self-form {
    background: #f5f5f5;
    min-height: 100vh;
    padding: 16px;

    .simple-title {
      font-size: 20px;
      font-weight: bold;
      color: #333;
      margin-bottom: 16px;
      padding: 16px;
      background: white;
      border-radius: 6px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .form-card {
      background: white;
      border-radius: 6px;
      margin-bottom: 16px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

      .form-card-header {
        padding: 16px 20px;
        border-bottom: 1px solid #f0f0f0;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .form-card-title {
          font-size: 16px;
          font-weight: 600;
          color: #333;
          display: flex;
          align-items: center;

          .title-icon {
            margin-right: 8px;
            color: #1890ff;
          }
        }
      }

      .form-card-body {
        padding: 20px;
      }
    }

    .form-footer {
      background: white;
      padding: 16px 20px;
      border-radius: 6px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      text-align: center;
    }

    // 帮助文本样式
    .help-text {
      font-size: 12px;
      color: #909399;
      line-height: 1.5;
      margin-top: 5px;
    }

    // 提示图标样式
    .tooltip-icon {
      color: #909399;
      margin-left: 5px;
      cursor: pointer;
    }
  }
</style>